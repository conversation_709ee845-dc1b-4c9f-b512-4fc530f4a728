import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Badge from '@shape-construction/arch-ui/src/Badge';
import Button from '@shape-construction/arch-ui/src/Button';
import { InformationCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import Popover from '@shape-construction/arch-ui/src/Popover';
import { Tooltip, TooltipContent, TooltipTrigger } from '@shape-construction/arch-ui/src/Tooltip/Tooltip';
import { getHeatmapColorClasses, HEATMAP_LEVELS, HEATMAP_THEME } from './heatmap-config';
import { ScoringInfo } from './ScoringInfo';

export const HeatmapLegend = () => {
  const messages = useMessageGetter('dataBook.page.heatmapDashboard');
  return (
    <div className="overflow-x-auto">
      <div className="flex flex-row justify-between px-4 min-w-max items-center">
        <Popover>
          <Popover.Trigger>
            <Tooltip delayDuration={100}>
              <TooltipTrigger>
                <Button color="secondary" size="xxs" variant="outlined" leadingIcon={InformationCircleIcon}>
                  {messages('performanceDetails.issueReportsTable.scoringInfo.scoringInfoCTA')}
                </Button>
              </TooltipTrigger>
              <TooltipContent align="start">
                {messages('performanceDetails.issueReportsTable.scoringInfo.tooltipText')}
              </TooltipContent>
            </Tooltip>
          </Popover.Trigger>
          <Popover.Content align="start" className="p-0 max-h-[340px]">
            <ScoringInfo />
          </Popover.Content>
        </Popover>
        <div className="flex">
          {HEATMAP_LEVELS.slice(1).map((level) => {
            const badgeClasses = getHeatmapColorClasses(HEATMAP_THEME, level, 'badge');
            return (
              <div key={level} className="p-2 last:pr-0">
                <Badge label={messages(`healthLevels.${level}.label`)} className={badgeClasses} />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
